plugins{
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
    id 'androidx.navigation.safeargs'
    id 'org.jetbrains.kotlin.android'
    id 'org.sonarqube' version '3.0'
}

//apply plugin: "androidx.navigation.safeargs"
def getMyVersionCode = { ->
    def code = project.hasProperty('versionCode') ? versionCode.toInteger() : 02
    println "VersionCode is set to $code"
    return code
}

def secretsPropertiesFile = rootProject.file("secrets.properties")
def secretProperties = new Properties()

if (secretsPropertiesFile.exists()) {
    secretProperties.load(new FileInputStream(secretsPropertiesFile))
} else {
    secretProperties.setProperty("SIGNING_KEYSTORE_PASSWORD", "${System.getenv('SIGNING_KEYSTORE_PASSWORD')}")
    secretProperties.setProperty("SIGNING_KEY_ALIAS", "${System.getenv('SIGNING_KEY_ALIAS')}")
    secretProperties.setProperty("SIGNING_KEY_PASSWORD", "${System.getenv('SIGNING_KEY_PASSWORD')}")
}

android {
    namespace 'com.bodymount.app'
    compileSdk 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId 'com.bodymount.app'
        minSdkVersion 26
        targetSdkVersion 34
        versionCode getMyVersionCode()
        versionName '2.01.**********'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        //buildConfigField "String", "SCI_CHART_LICENSE_KEY", "\"$SCI_CHART_LICENSE_KEY\""
    }
    buildFeatures {
        viewBinding true
        dataBinding true
    }
    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        test.java.srcDirs += "src/test/kotlin"
        androidTest.java.srcDirs += "src/androidTest/kotlin"
    }

    buildFeatures {
        viewBinding true
    }

    signingConfigs {
        release {
            // keytool -genkey -v -keystore android-signing-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias sibel-dev
            storeFile file("../android-signing-keystore.jks")
            storePassword "password"        //storePassword -> 6aV_K7NgGP4dxTJJ
            keyAlias "src"
            keyPassword "password"          //keyPassword -> 6aV_K7NgGP4dxTJJ
        }
    }

    buildTypes {
        debug {
            testCoverageEnabled true
        }
        release {
            minifyEnabled false
            shrinkResources false
            testCoverageEnabled false
            proguardFiles getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro"
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }


    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures.dataBinding = true

    lintOptions {
        disable "ContentDescription"
    }

    testOptions {
        execution 'ANDROIDX_TEST_ORCHESTRATOR'
        animationsDisabled true
        unitTests.returnDefaultValues = true
        unitTests.includeAndroidResources = true
        unitTests.all {
            useJUnitPlatform()
            jvmArgs "-noverify", "-ea"
        }
    }
}

apply plugin: "org.sonarqube"
sonarqube{
    properties{
        property "sonar.projectName", "com.bodymount.app"
        property "sonar.projectKey", "com.bodymount.app"
        property "sonar.host.url", "http://**********:9000"
        property "sonar.language", "kotlin"
        property "sonar.sources", "src/main/java/"
        property "sonar.login", "admin"
        property "sonar.password", "iOrbit@2023"
    }
}

allprojects{
    repositories {
        maven {url 'https://jitpack.io'}
    }
}

dependencies {
    implementation 'no.nordicsemi.android.support.v18:scanner:1.4.5'
    implementation 'no.nordicsemi.android:ble:2.6.1'
    implementation "no.nordicsemi.android:dfu:2.0.3"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation project(":core")
    implementation project(":bluetooth")
    implementation project(":nfc")
    implementation fileTree(dir: "libs", include: ["*.jar"])

    //noinspection GradleDependency
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.0"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4"
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.7.10"
    implementation "androidx.appcompat:appcompat:1.4.2"
    implementation "androidx.constraintlayout:constraintlayout:2.1.4"
    implementation 'androidx.recyclerview:recyclerview:1.3.1'
    //noinspection GradleDependency
    implementation "androidx.navigation:navigation-fragment-ktx:2.3.0"
    //noinspection GradleDependency
    implementation "androidx.navigation:navigation-ui-ktx:2.3.0"
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'com.androidplot:androidplot-core:1.5.9'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.0-alpha03'
    androidTestImplementation 'androidx.test.ext:junit:1.2.0-alpha03'
    androidTestImplementation 'androidx.test:rules:1.6.0-alpha03'
    debugImplementation "androidx.fragment:fragment-testing:1.5.0"
    compileOnly "javax.annotation:jsr250-api:1.0"

    implementation "com.google.android.flexbox:flexbox:3.0.0"
    implementation "com.google.accompanist:accompanist-permissions:0.21.1-beta"

    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    implementation 'androidx.room:room-runtime:2.5.2'
    kapt "androidx.room:room-compiler:2.5.2"
    kapt "com.android.databinding:compiler:3.1.4"

    implementation 'androidx.room:room-ktx:2.5.2'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4'

    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'

    implementation 'android.arch.lifecycle:extensions:1.1.1'
    implementation 'com.google.android.material:material:1.9.0'

    implementation 'com.mikepenz:iconics-core:5.1.0'
    implementation 'com.mikepenz:iconics-views:5.1.0'

    //noinspection GradleCompatible
    implementation 'com.android.support:appcompat-v7:28.0.0'
    //FhirApiDependencies
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'
    implementation 'com.google.code.gson:gson:2.9.1'

    //Library for local storage write and retrieve
    implementation 'androidx.core:core-ktx:1.10.1'

    implementation 'com.github.tobiasschuerg:android-prefix-suffix-edit-text:1.3.1'

    implementation("com.opencsv:opencsv:5.6")

    /*classpath 'io.realm:realm-gradle-plugin:6.0.2'
    classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:3.4.0.2513"*/

    implementation 'net.zetetic:android-database-sqlcipher:4.4.2'

/*---------------------------------------------------------------------------*/
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    kapt 'com.github.bumptech.glide:compiler:4.12.0'
/*---------------------------------------------------------------------------*/

}
