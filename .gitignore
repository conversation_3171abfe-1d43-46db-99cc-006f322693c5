
src/.gradle/7.3.3/checksums/checksums.lock
src/.gradle/7.3.3/checksums/md5-checksums.bin
src/.gradle/7.3.3/checksums/sha1-checksums.bin
src/.gradle/7.3.3/executionHistory/executionHistory.lock
src/.gradle/7.3.3/fileHashes/fileHashes.lock
src/.gradle/buildOutputCleanup/buildOutputCleanup.lock
src/.gradle/buildOutputCleanup/outputFiles.bin
src/.gradle/file-system.probe
src/.idea/workspace.xml
/.idea/zencoder/chats/1f19db49-ff8d-4ec8-9eac-2fbb9b10827e.json
/.idea/zencoder/chats/68ebaa67-0506-4d57-86ea-48d3f2c77113.json
/.idea/AndroidProjectSystem.xml
/.idea/AugmentWebviewStateStore.xml
/weatherlib/build.gradle.kts
/src/bluetooth/build/.transforms/3a220cb309900026cc0bd4345f10caf2/transformed/jetified-bluetooth-runtime/jetified-bluetooth-runtime_dex/classes.dex
/.idea/deploymentTargetSelector.xml
/.idea/deviceManager.xml
/.idea/caches/deviceStreaming.xml
/.idea/zencoder/chats/ebf72098-1e36-4402-828f-5aac028cac8f.json
/.idea/gradle.xml
/.idea/kotlinc.xml
/.idea/migrations.xml
/.idea/misc.xml
/.zencoder/rules/repo.md
/src/bluetooth/build/.transforms/3a220cb309900026cc0bd4345f10caf2/results.bin
/.idea/runConfigurations.xml
